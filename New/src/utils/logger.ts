/**
 * Centralized Logging Utility for DermaCare DataSync
 *
 * Provides standardized logging functions with consistent formatting,
 * request ID tracking, ISO timestamps, and log level filtering.
 *
 * Features:
 * - Consistent log format: [requestId] [timestamp] message
 * - Log level filtering (DEBUG, INFO, WARN, ERROR)
 * - Production mode support with reduced verbosity
 * - Request context propagation
 * - Performance-optimized for production environments
 */

import { getConfig, type LogLevel, type LoggingConfig } from "@config";

/**
 * Log level hierarchy for filtering
 */
const LOG_LEVELS: Record<LogLevel, number> = {
	DEBUG: 0,
	INFO: 1,
	WARN: 2,
	ERROR: 3,
};

/**
 * Check if a log level should be output based on current configuration
 */
const shouldLog = (level: LogLevel): boolean => {
	const config = getConfig('logging') as LoggingConfig;
	const currentLevelValue = LOG_LEVELS[config.level];
	const messageLevelValue = LOG_LEVELS[level];
	return messageLevelValue >= currentLevelValue;
};

/**
 * Format log message with consistent structure
 */
const formatLogMessage = (
	requestId: string,
	level: LogLevel,
	message: string,
	data?: unknown
): string => {
	const timestamp = new Date().toISOString();
	const baseMessage = `[${requestId}] [${timestamp}] [${level}] ${message}`;
	
	if (data !== undefined) {
		const config = getConfig('logging') as LoggingConfig;
		if (config.includeDebugInfo) {
			return `${baseMessage}\nData: ${JSON.stringify(data, null, 2)}`;
		} else {
			// In production, limit data output to prevent log spam
			const dataStr = typeof data === 'object' && data !== null 
				? JSON.stringify(data).substring(0, 200) + '...'
				: String(data).substring(0, 200);
			return `${baseMessage} | Data: ${dataStr}`;
		}
	}
	
	return baseMessage;
};

/**
 * Log debug messages (lowest priority)
 * Only shown when LOG_LEVEL is DEBUG
 */
export const logDebug = (requestId: string, message: string, data?: unknown): void => {
	if (shouldLog('DEBUG')) {
		console.log(formatLogMessage(requestId, 'DEBUG', message, data));
	}
};

/**
 * Log informational messages
 * Shown when LOG_LEVEL is DEBUG or INFO
 */
export const logInfo = (requestId: string, message: string, data?: unknown): void => {
	if (shouldLog('INFO')) {
		console.log(formatLogMessage(requestId, 'INFO', message, data));
	}
};

/**
 * Log warning messages
 * Shown when LOG_LEVEL is DEBUG, INFO, or WARN
 */
export const logWarn = (requestId: string, message: string, data?: unknown): void => {
	if (shouldLog('WARN')) {
		console.warn(formatLogMessage(requestId, 'WARN', message, data));
	}
};

/**
 * Log error messages (highest priority)
 * Always shown regardless of log level
 */
export const logError = (requestId: string, message: string, error?: unknown): void => {
	if (shouldLog('ERROR')) {
		const errorData = error instanceof Error 
			? { message: error.message, stack: error.stack }
			: error;
		console.error(formatLogMessage(requestId, 'ERROR', message, errorData));
	}
};

/**
 * Log API performance metrics
 * Special formatting for API call performance tracking
 */
export const logApiPerformance = (
	requestId: string,
	method: string,
	status: number,
	duration: number,
	url: string
): void => {
	if (shouldLog('INFO')) {
		const message = `API Call: [${method}] [${status}] -> ${duration.toFixed(2)}s -> ${url}`;
		console.log(formatLogMessage(requestId, 'INFO', message));
	}
};

/**
 * Log database operation performance
 * Special formatting for database operation tracking
 */
export const logDbPerformance = (
	requestId: string,
	operation: string,
	duration: number,
	details?: string
): void => {
	if (shouldLog('INFO')) {
		const message = `DB Operation: ${operation} -> ${duration.toFixed(2)}ms${details ? ` -> ${details}` : ''}`;
		console.log(formatLogMessage(requestId, 'INFO', message));
	}
};

/**
 * Log processing step completion
 * Used for tracking major processing milestones
 */
export const logProcessingStep = (
	requestId: string,
	step: string,
	duration?: number,
	details?: unknown
): void => {
	if (shouldLog('INFO')) {
		const durationStr = duration ? ` (${duration}ms)` : '';
		const message = `Processing: ${step}${durationStr}`;
		console.log(formatLogMessage(requestId, 'INFO', message, details));
	}
};

/**
 * Log custom field operations
 * Specialized logging for custom field processing
 */
export const logCustomField = (
	requestId: string,
	operation: string,
	fieldName: string,
	details?: unknown
): void => {
	if (shouldLog('DEBUG')) {
		const message = `Custom Field ${operation}: ${fieldName}`;
		console.log(formatLogMessage(requestId, 'DEBUG', message, details));
	}
};

/**
 * Log webhook events
 * Specialized logging for webhook processing
 */
export const logWebhook = (
	requestId: string,
	event: string,
	model: string,
	entityId?: string | number
): void => {
	if (shouldLog('INFO')) {
		const message = `Webhook: ${event} -> ${model}${entityId ? ` -> ID: ${entityId}` : ''}`;
		console.log(formatLogMessage(requestId, 'INFO', message));
	}
};

/**
 * Legacy console.log replacement
 * Provides backward compatibility while encouraging migration to structured logging
 * @deprecated Use specific log functions (logInfo, logError, etc.) instead
 */
export const log = (requestId: string, message: string, data?: unknown): void => {
	logInfo(requestId, message, data);
};

/**
 * Get current logging configuration
 * Useful for debugging logging issues
 */
export const getLoggingConfig = (): LoggingConfig => {
	return getConfig('logging') as LoggingConfig;
};

/**
 * Check if debug logging is enabled
 * Useful for conditional expensive debug operations
 */
export const isDebugEnabled = (): boolean => {
	return shouldLog('DEBUG');
};

/**
 * Check if production mode is enabled
 * Useful for conditional production optimizations
 */
export const isProductionMode = (): boolean => {
	return (getConfig('logging') as LoggingConfig).isProduction;
};
