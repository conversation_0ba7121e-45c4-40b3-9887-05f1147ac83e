{"event": "EntityWasCreated", "model": "Patient", "id": 1767, "payload": {"id": 1767, "createdAt": "2025-07-25T19:12:52.000Z", "updatedAt": "2025-07-25T19:12:52.000Z", "createdBy": 5003, "updatedBy": 5003, "firstName": "Sint mollit consequa", "lastName": "Voluptatem dolorem q", "dob": "1942-10-12T00:00:00.000Z", "ssn": "<PERSON>si aperiam dolores", "flashMessage": "", "active": true, "phoneMobile": "", "phonePersonal": null, "phoneBusiness": null, "email": "<EMAIL>", "title": null, "titleSuffix": null, "healthInsurance": null, "gender": "<PERSON><PERSON><PERSON>", "addresses": [{"id": 1758, "label": null, "name": null, "street": "Dolores non rerum vo", "streetNumber": "Obcaecati exercitati", "postalCode": "<PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON> blanditiis", "country": "KH", "primary": 1}], "categories": [], "customFields": [11310, 11311, 11312, 11313, 11314], "invoices": [], "payments": [], "files": [], "history": [], "appointments": [], "messages": [], "medications": [], "personalWebForms": [], "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1765.png", "avatarUrl": null}, "timestamp": "2025-07-25T19:12:53.740Z"}